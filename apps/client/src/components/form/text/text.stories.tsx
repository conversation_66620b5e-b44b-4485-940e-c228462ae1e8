import type { <PERSON>a, StoryObj } from "@storybook/react";
import { User } from "lucide-react";
import { z } from "zod/v4";
import { Form } from "../form";
import { Text } from "./text";

// Mock form data type for stories
type MockFormData = {
    firstName: string;
};

// Mock schema for form validation
const mockSchema = z.object({
    firstName: z.string().min(3, "Username must be at least 3 characters").optional(),
});

const meta = {
    title: "Form/Text",
    component: Text,
    parameters: {
        layout: "centered",
    },
    argTypes: {},
    // decorators: [
    //     (Story, context) => (
    //         <Form
    //             editMode={context.parameters?.editMode || "classic"}
    //             defaultValues={context.parameters?.defaultValues || {}}
    //             schema={mockSchema}
    //             onSubmit={() => {}}
    //         >
    //             <Story />
    //         </Form>
    //     ),
    // ],
} satisfies Meta<typeof Text<MockFormData>>;

export default meta;
type Story = StoryObj<typeof meta>;

export const EditModeComparison: Story = {
    args: {
        name: "firstName" as keyof MockFormData,
        label: "Edit Mode Comparison",
    },
    render: () => (
        <div className="w-200 space-y-8 border-1 p-5">
            <div>
                <h3 className="mb-4 text-lg font-semibold">Classic Edit Mode</h3>
                <Form
                    editMode="classic"
                    defaultValues={{ firstName: "john_doe" }}
                    schema={mockSchema}
                    onSubmit={() => {}}
                >
                    <Text<MockFormData>
                        name="firstName"
                        label="First name"
                        placeholder="Enter your firstName"
                        startAdornment={<User className="h-4 w-4" />}
                    />
                </Form>
            </div>
            <div>
                <h3 className="mb-4 text-lg font-semibold">Inline Edit Mode - Editable</h3>
                <Form
                    editMode="inline"
                    defaultValues={{ firstName: "john_doe" }}
                    schema={mockSchema}
                    onSubmit={() => {}}
                >
                    <Text<MockFormData>
                        name="firstName"
                        label="First name"
                        placeholder="Click to edit"
                        startAdornment={<User className="h-4 w-4" />}
                    />
                </Form>
            </div>
            <div>
                <h3 className="mb-4 text-lg font-semibold">Inline Edit Mode - Readonly</h3>
                <Form
                    editMode="inline"
                    defaultValues={{ firstName: "john_doe" }}
                    schema={mockSchema}
                    onSubmit={() => {}}
                >
                    <Text<MockFormData>
                        name="firstName"
                        label="First name"
                        placeholder="Double-click to select"
                        readonly
                        startAdornment={<User className="h-4 w-4" />}
                    />
                </Form>
            </div>
        </div>
    ),
};
